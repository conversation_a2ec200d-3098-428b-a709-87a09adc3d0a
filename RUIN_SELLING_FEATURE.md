# Automatic Ruin Selling Feature

## Overview
The GoodyHut collection system now includes automatic ruin selling functionality that processes completed instances to clear ruins and make them available for future collection cycles.

## ✅ Implementation Complete

### 1. **Instance State Detection** ✅
- Detects instances in "COMPLETED_AWAITING" state (job complete but not collectible)
- Identifies GEMS instances that have been collected but still have ruins/debris
- Processes these instances during batch validation phase

### 2. **Ruin Detection Methods** ✅
- `HasRuins()` - Check for standard ruins
- `HasD<PERSON><PERSON>()` - Check for debris/wreckage
- `CanSell()` - Check if instance can be sold
- `CanClear()` - Check if instance can be cleared
- Supports multiple ruin types and clearing methods

### 3. **Selling Operations** ✅
- `SellRuins()` - Sell standard ruins
- `ClearDebris()` - Clear debris/wreckage
- `DoSell()` - General sell operation
- `DoClear()` - General clear operation
- Automatic method selection based on ruin type

### 4. **Integration with Batch Processing** ✅
- Integrated into `validateBatchWithState()` method
- Processes completed instances alongside collectible validation
- Maintains separate statistics for ruin selling operations
- Works seamlessly with existing state persistence

### 5. **Statistics and Tracking** ✅
- Success/failure counts for ruin selling operations
- Persistent tracking across script restarts
- Integration with progress reporting and state management
- Separate enable/disable control for ruin selling

## 🔧 Technical Implementation

### Ruin Detection Logic
```typescript
private checkForSellableRuins(validation: ValidatedInstance): { hasRuins: boolean, ruinType?: string, sellMethod?: string } {
    // Check multiple ruin indicators
    const hasRuinsResult = this.safeInvoke(validation.instance, "HasRuins");
    const hasDebrisResult = this.safeInvoke(validation.instance, "HasDebris");
    const canSellResult = this.safeInvoke(validation.instance, "CanSell");
    const canClearResult = this.safeInvoke(validation.instance, "CanClear");
    
    // Return appropriate selling method based on ruin type
}
```

### Selling Execution
```typescript
private executeRuinSelling(validation: ValidatedInstance, ruinInfo: { ruinType: string, sellMethod: string }): boolean {
    // Execute the appropriate selling/clearing method
    const sellResult = this.safeInvoke(validation.instance, ruinInfo.sellMethod);
    // Track success/failure statistics
    // Return operation result
}
```

### Batch Integration
```typescript
// During batch validation, check for completed instances
if (validation.state === "COMPLETED_AWAITING" && validation.rewardType === "GEMS") {
    const ruinProcessed = await this.processCompletedInstances(validation);
    if (ruinProcessed) {
        // Instance cleared and may be available for future collection
    }
}
```

## 🎮 User Interface

### New Commands
```javascript
// Enable/disable ruin selling
goodyManager.enableRuinSelling()
goodyManager.disableRuinSelling()

// Check ruin selling statistics
goodyManager.getRuinStats()
```

### Enhanced Progress Display
```
📊 Collections: 145 started, 140 instant
🗑️ Ruins processed: 23
⚡ Hook: Active (1,250 calls)
💎 DoJobBuyThrough: 140 success, 5 failures
🗑️ Ruin Selling: 23 success, 2 failures
```

### Batch Processing Output
```
🔍 Validating batch instances 101-110...
  🗑️ Found completed GEMS instance (EntityController 103) - checking for ruins...
    💎 Found ruins that can be sold/cleared
    🗑️ Attempting to sell/clear ruins using SellRuins...
    ✅ Successfully sold/cleared ruins!
    🎉 Instance cleared and ready for future collection!
  ✅ Ruins cleared for EntityController 103 - may be available for future collection
🗑️ Processed ruins for 1 completed instances
```

## 📊 State Persistence Integration

### Enhanced State Structure
```typescript
interface CollectionState {
    // ... existing fields ...
    ruinSellStats: {
        successCount: number;
        failureCount: number;
        enabled: boolean;
    };
    sessionStats: {
        totalCollectionsStarted: number;
        totalInstantCompletions: number;
        totalRuinsProcessed: number;  // New field
        startTime: number;
    };
}
```

### Persistent Statistics
- Ruin selling success/failure counts saved across restarts
- Enable/disable state persisted
- Total ruins processed tracked in session statistics
- All statistics displayed in progress reports

## 🛡️ Error Handling

### Safe Operation
- Uses existing `safeInvoke()` method for all ruin operations
- Graceful handling of missing methods or invalid instances
- Continues processing even if individual ruin selling fails
- Tracks failures for monitoring and debugging

### Validation After Selling
- Re-validates instance state after successful ruin selling
- Confirms instance is now in "IDLE_READY" state
- Provides feedback on clearing success
- Handles cases where clearing doesn't make instance immediately available

## 🎯 Benefits

### Efficiency
- **Automatic Cleanup**: Clears completed instances without manual intervention
- **Future Availability**: Makes instances available for subsequent collection cycles
- **Batch Integration**: Processes ruins during normal batch operations
- **No Extra Passes**: Integrated into existing validation workflow

### User Experience
- **Transparent Operation**: Automatic processing with clear logging
- **Controllable**: Can be enabled/disabled as needed
- **Trackable**: Statistics show how many ruins have been processed
- **Persistent**: Settings and statistics survive script restarts

### Game Optimization
- **Instance Recycling**: Cleared instances can be collected again in future
- **Resource Management**: Removes debris/ruins that block reuse
- **Cycle Efficiency**: Maximizes available instances for collection

## 🚀 Usage Examples

### Enable Ruin Selling
```javascript
// Enable automatic ruin selling (default: enabled)
goodyManager.enableRuinSelling()

// Start batch collection with ruin selling
goodyManager.batchCollection()
```

### Monitor Ruin Selling
```javascript
// Check ruin selling statistics
goodyManager.getRuinStats()
// Returns: { enabled: true, successes: 23, failures: 2 }

// View detailed progress including ruins
goodyManager.showProgress()
```

### Disable if Needed
```javascript
// Disable ruin selling if causing issues
goodyManager.disableRuinSelling()

// Continue batch collection without ruin selling
goodyManager.batchCollection()
```

## ✅ Integration Complete

The ruin selling feature is fully integrated with:
- ✅ Existing state persistence system
- ✅ Batch processing workflow
- ✅ Statistics tracking and reporting
- ✅ Error handling and recovery
- ✅ User interface and controls
- ✅ Progress monitoring and display

This enhancement works seamlessly alongside existing functionality and provides automatic cleanup of completed instances to maximize collection efficiency across multiple cycles.
