# State Persistence Testing Guide

## Quick Verification Steps

### 1. **Test Fresh Start**
```javascript
// Load the script and verify startup message
// Should show: "No saved progress found"

// Start fresh collection
goodyManager.batchCollection()

// Let it process a few batches, then stop the script
```

### 2. **Test State Persistence**
```javascript
// Reload the script
// Should show: "Progress: X instances processed, Y/Z batches completed"

// Verify state file exists
// Check for goodyHutState.json in workspace root

// Resume collection
goodyManager.resume()

// Should continue from where it left off, skipping processed instances
```

### 3. **Test Reset Functionality**
```javascript
// With existing progress, force reset
goodyManager.reset()

// Should clear all progress and start fresh
// State file should be cleared
```

### 4. **Test Progress Commands**
```javascript
// Check progress status
goodyManager.getProgress()

// Show detailed progress
goodyManager.showProgress()

// Should show comprehensive information about current state
```

## Expected Behaviors

### On Script Load
- **With Progress**: Shows summary and resume options
- **Without Progress**: Shows fresh start guidance
- **Corrupted State**: Automatically clears and starts fresh

### During Collection
- **Progress Saving**: State saved after each batch completion
- **Instance Skipping**: Already processed instances are skipped
- **Statistics Tracking**: Hook calls and success rates maintained

### After Restart
- **Smart Resume**: Continues from last completed batch
- **Duplicate Prevention**: Skips already processed EntityController indices
- **Statistics Restoration**: Previous session stats are restored

## File System Verification

### State File Location
```
c:\Users\<USER>\dominations\goodyHutState.json
```

### State File Content Check
```json
{
  "version": 2,
  "timestamp": [recent timestamp],
  "processedEntityIndices": [array of numbers],
  "completedBatches": [array of batch numbers],
  "totalBatches": [number],
  "currentBatchIndex": [number],
  "hookStats": { "installed": true/false, "callCount": [number] },
  "buyThroughStats": { "successCount": [number], "failureCount": [number], "enabled": true/false },
  "sessionStats": { "totalCollectionsStarted": [number], "totalInstantCompletions": [number], "startTime": [timestamp] }
}
```

## Error Scenarios to Test

### 1. **Corrupted State File**
- Manually corrupt the JSON file
- Reload script - should detect corruption and start fresh
- Should show: "State consistency check failed, starting fresh"

### 2. **Missing State File**
- Delete goodyHutState.json
- Reload script - should start fresh
- Should show: "No saved progress found"

### 3. **File Permission Issues**
- Make state file read-only
- Try to save progress - should fallback to memory
- Should show: "Fallback: State saved to memory"

## Success Indicators

### ✅ Persistence Working
- State file created and updated during collection
- Progress restored after script reload
- Already processed instances are skipped
- Statistics carry over between sessions

### ✅ Error Handling Working
- Corrupted state files are detected and cleared
- File I/O errors fallback to memory storage
- Invalid state data is rejected with fresh start

### ✅ User Experience Working
- Clear startup messages about progress status
- Helpful recommendations for next actions
- Detailed progress information available
- Distinct resume vs reset functionality

## Troubleshooting

### If State Not Persisting
1. Check file permissions in workspace directory
2. Verify Node.js fs module is available in Frida context
3. Look for error messages about file operations
4. Check if fallback to memory storage is occurring

### If State Corruption Issues
1. Delete goodyHutState.json manually
2. Restart script - should create fresh state
3. Monitor for validation error messages
4. Check state file format matches expected structure

### If Resume Not Working
1. Verify state file contains processedEntityIndices array
2. Check that batch numbers are reasonable
3. Ensure timestamps are not expired (24h limit)
4. Look for validation failure messages

## Performance Verification

### Expected Improvements
- **No Duplicate Work**: Previously processed instances should be skipped
- **Faster Startup**: Resume should be faster than fresh start
- **Progress Continuity**: Statistics should accumulate across sessions
- **Efficient Batching**: Should continue from last incomplete batch

### Monitoring Points
- Watch for "Skipped X already processed instances" messages
- Verify batch numbers continue from previous session
- Check that hook call counts accumulate properly
- Confirm collection statistics carry forward

This testing approach will verify that the state persistence system is working correctly and providing the expected benefits for large batch collection operations.
