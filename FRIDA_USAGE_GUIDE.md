# Frida Ruin Seller - Usage Guide

## Quick Start 🚀

### **Prerequisites**
- Frida installed on your system
- Android device with Dominations game installed
- USB debugging enabled on Android device
- TypeScript compiler (optional, for compilation)

### **Step 1: Prepare the Script**
```bash
# Option A: Use TypeScript directly (if frida supports .ts files)
# No compilation needed - use frida-ruin-seller.ts directly

# Option B: Compile to JavaScript
npx tsc frida-ruin-seller.ts --target es2017 --lib es2017 --outFile frida-ruin-seller.js
```

### **Step 2: Connect to Device**
```bash
# List connected devices
frida-ls-devices

# Should show something like:
# Id                                        Type    Name
# ----------------------------------------  ------  --------
# local                                     local   Local System
# 1234567890abcdef                         usb     Android Device
```

### **Step 3: Inject Script**
```bash
# Using TypeScript file (if supported)
frida -U -l frida-ruin-seller.ts com.nexonm.dominations.adk

# Using compiled JavaScript
frida -U -l frida-ruin-seller.js com.nexonm.dominations.adk

# With additional options for better output
frida -U -l frida-ruin-seller.js --no-pause com.nexonm.dominations.adk
```

## Expected Execution Flow 📊

### **Phase 1: Initialization (2-5 seconds)**
```
🗑️ Frida Ruin Seller v1.0 - Starting initialization...
🚀 Frida Ruin Seller - Java.perform() context established
🗑️ Frida Ruin Seller - Starting automated ruin selling...
🔧 Initializing Il2Cpp domain...
✅ Il2Cpp domain initialized successfully
```

### **Phase 2: Discovery (10-30 seconds)**
```
🔍 Discovering EntityController instances...
✅ Discovered 4028 EntityController instances
🔍 Filtering instances for GoodyHut components with sellable ruins...
📊 Validation progress: 25.0% (1000/4028)
📊 Validation progress: 50.0% (2000/4028)
📊 Validation progress: 75.0% (3000/4028)
📊 Validation progress: 100.0% (4028/4028)
🎯 Found 3 instances ready for ruin selling
📊 Summary: 1247 GoodyHuts, 156 completed
```

### **Phase 3: Processing (Variable duration)**
```
🗑️ Starting ruin selling operations...
🗑️ Processing 1/3: EntityController 45
   State: COMPLETED_AWAITING, Reward: 25 GEMS
🗑️ [45] Attempting to sell ruins...
✅ SellRuins succeeded with parameters: [true]
✅ [45] Successfully executed SellRuins
✅ [45] Ruin selling completed successfully
```

### **Phase 4: Final Report**
```
============================================================
🗑️ FRIDA RUIN SELLER - EXECUTION REPORT
============================================================
📊 Instance Discovery:
   Total Instances: 4028
   Valid GoodyHuts: 1247
   Completed Instances: 156

🗑️ Ruin Selling Operations:
   Attempts: 3
   Successful: 2
   Failed: 1
   Success Rate: 66.7%

⏱️ Performance Metrics:
   Execution Time: 12450ms (12.45s)
   Operations/Second: 0.24
============================================================
✅ Successfully processed 2 ruin selling operations!
🗑️ Frida Ruin Seller execution completed.
```

## Troubleshooting 🛠️

### **Common Issues and Solutions**

#### **"Failed to get Assembly-CSharp image"**
```
❌ Failed to get Assembly-CSharp image
```
**Solution**: Game may not be fully loaded. Wait a few seconds and try again, or increase the initialization delay in the script.

#### **"No EntityController instances found"**
```
❌ No EntityController instances found
```
**Solution**: Make sure you're in the main game view (not menus or loading screens). The script needs access to the game world.

#### **"bad argument count" errors**
```
⚠️ CanCollect invocation failed, trying parameter variations: bad argument count
✅ CanCollect succeeded with parameters: [true]
```
**Solution**: This is normal! The script automatically discovers correct parameters. The warning shows the discovery process working.

#### **Script hangs during validation**
**Solution**: Large numbers of instances (4000+) can take time to validate. Wait for progress updates or reduce the validation scope if needed.

### **Performance Optimization**

#### **For Large Instance Counts (4000+ instances)**
- Expect 10-30 seconds for validation phase
- Processing time depends on number of completed instances
- Each ruin selling operation takes ~200ms due to safety delays

#### **For Better Performance**
- Close other apps to free memory
- Ensure stable USB connection
- Run when game is idle (not during active gameplay)

### **Safety Considerations**

#### **Game State Requirements**
- Run in main game view (world map visible)
- Avoid running during active gameplay or battles
- Don't run during game updates or maintenance

#### **Resource Usage**
- Script uses minimal CPU and memory
- No permanent changes to game files
- Safe to interrupt with Ctrl+C if needed

## Advanced Usage 🔧

### **Manual Execution Control**
```javascript
// Access the class directly in Frida console
var ruinSeller = new FridaRuinSeller();
ruinSeller.run();
```

### **Custom Configuration**
The script can be modified to adjust:
- Validation criteria (which instances to target)
- Processing delays (speed vs safety balance)
- Logging verbosity (more or less detailed output)
- Method selection (which ruin selling methods to try)

### **Integration with Other Scripts**
The script is designed to be non-conflicting and can run alongside other Frida scripts that don't interfere with GoodyHut operations.

## Expected Results 🎯

### **Typical Execution Statistics**
- **Total Instances**: 3000-5000 (varies by game progress)
- **Valid GoodyHuts**: 800-1500 (depends on base development)
- **Completed Instances**: 50-200 (varies by collection activity)
- **Ruin Selling Candidates**: 1-20 (depends on ruins accumulated)
- **Success Rate**: 70-90% (depends on game version compatibility)
- **Execution Time**: 10-60 seconds (depends on instance count)

### **When to Run the Script**
- **After long periods away from game**: More completed instances likely
- **Before starting new collection cycles**: Clear ruins to make space
- **During maintenance periods**: Safe time for cleanup operations
- **When inventory is full**: Clear ruins to free up space

## Support and Debugging 🐛

### **Enable Detailed Logging**
The script already provides comprehensive logging. For even more detail, modify the logging levels in the source code.

### **Common Success Indicators**
- ✅ Il2Cpp domain initialized successfully
- ✅ Discovered [X] EntityController instances
- ✅ [methodName] succeeded with parameters: [...]
- ✅ Successfully processed [X] ruin selling operations!

### **When to Seek Help**
- Consistent initialization failures
- Zero instances discovered in active game
- All ruin selling operations failing
- Script crashes or hangs indefinitely

The Frida Ruin Seller is designed to be robust and self-recovering, handling most edge cases automatically! 🎉
