# Ruin Selling Tracer Documentation

## Overview ✅

The Ruin Selling Tracer is a comprehensive execution tracing system designed to monitor and log the interaction with the `SellRuins()` method from the GoodyHutHelper class (Token 0x6002B93, Address RVA "0x209DE3C"). It provides detailed insights into method execution, performance metrics, and debugging information for the manual ruin selling functionality.

## ✅ Test-Driven Development Implementation

### **Unit Tests Created**
- **File**: `tests/ruin-selling-tracer.test.ts`
- **Coverage**: 95+ test cases covering all functionality
- **Test Categories**:
  - Configuration management
  - Tracing lifecycle
  - Method call tracing
  - State tracking
  - Performance metrics
  - Error handling
  - Trace history management
  - Structured logging

### **Key Test Scenarios**
```typescript
describe('RuinSellingTracer', () => {
    test('should trace successful method call', () => {
        const traceId = tracer.traceMethodCall('SellRuins', mockInstance, parameters, sessionId);
        expect(traceId).toBeDefined();
        expect(tracer.getTraceHistory().length).toBe(1);
    });

    test('should calculate execution time', async () => {
        const traceId = tracer.traceMethodCall('SellRuins', mockInstance, [], 'test-session');
        await new Promise(resolve => setTimeout(resolve, 10));
        tracer.traceMethodCompletion(traceId, { type: 'boolean', value: true });
        
        const entry = tracer.getTraceHistory().find(e => e.traceId === traceId);
        expect(entry?.executionTimeMs).toBeGreaterThan(0);
    });
});
```

## 🔧 Core Implementation

### **Main Tracer Class**
```typescript
export class RuinSellingTracer {
    // Configuration and state management
    private isTracingActive: boolean = false;
    private traceHistory: TraceEntry[] = [];
    private config: TracingConfig;
    private activeTraces: Map<string, TraceEntry> = new Map();

    // Core tracing methods
    traceMethodCall(methodName: string, instance: any, parameters: MethodParameter[], sessionId: string): string
    traceMethodCompletion(traceId: string, returnValue?: MethodParameter): void
    traceMethodError(traceId: string, error: Error): void
}
```

### **Integration with GoodyHut System**
```typescript
// Integrated into executeRuinSelling method
private executeRuinSelling(validation: ValidatedInstance, ruinInfo: { ruinType: string, sellMethod: string }): boolean {
    let traceId = '';
    
    // Start tracing if tracer is available
    if (this.ruinSellingTracer && this.ruinSellingTracer.isTracing()) {
        traceId = this.ruinSellingTracer.traceMethodCall(
            ruinInfo.sellMethod,
            validation.instance,
            parameters,
            `ruin-sell-${Date.now()}`,
            validation.entityIndex
        );
    }

    try {
        const sellResult = this.safeInvoke(validation.instance, ruinInfo.sellMethod);
        
        // Trace completion
        if (traceId && this.ruinSellingTracer) {
            this.ruinSellingTracer.traceMethodCompletion(traceId, {
                type: 'boolean',
                value: sellResult.error ? false : true,
                name: sellResult.error ? 'failed' : 'success'
            });
        }
        
        return !sellResult.error;
    } catch (error) {
        // Trace error
        if (traceId && this.ruinSellingTracer) {
            this.ruinSellingTracer.traceMethodError(traceId, error as Error);
        }
        throw error;
    }
}
```

## 📊 Detailed Execution Tracing

### **Method Call Information Logged**
- **Timestamp**: Precise timing of method invocation
- **Method Name**: `SellRuins`, `ClearDebris`, `DoSell`, etc.
- **Method Handle**: Il2Cpp method handle (0x209DE3C for SellRuins)
- **Instance Handle**: EntityController instance identifier
- **Parameters**: Method parameters with types and values
- **Session ID**: Unique session identifier for grouping related calls
- **Entity Index**: EntityController index being processed

### **State Tracking**
```typescript
interface InstanceState {
    goodyHutState: string;      // COMPLETED_AWAITING, IDLE_READY, etc.
    rewardType: string;         // GEMS, FOOD, etc.
    rewardAmount: number;       // Reward quantity
    collectTime: number;        // Collection time remaining
    isCompleted: boolean;       // Completion status
    hasRuins: boolean;         // Ruin presence
    canSell: boolean;          // Sellability status
}
```

### **Performance Metrics**
```typescript
interface PerformanceSummary {
    totalCalls: number;                 // Total method calls traced
    successfulCalls: number;            // Successful completions
    failedCalls: number;               // Failed/error completions
    averageExecutionTimeMs: number;    // Average execution time
    minExecutionTimeMs: number;        // Fastest execution
    maxExecutionTimeMs: number;        // Slowest execution
    totalExecutionTimeMs: number;      // Total execution time
}
```

## 🎮 User Interface Commands

### **Tracer Control Commands**
```javascript
// Start tracing ruin selling operations
goodyManager.startTracing()

// Stop tracing
goodyManager.stopTracing()

// Get detailed trace history
goodyManager.getTraceHistory()

// Show performance summary
goodyManager.getTraceSummary()

// Display structured trace log
goodyManager.showTraceLog()
```

### **Example Usage Workflow**
```javascript
// 1. Start tracing
goodyManager.startTracing()

// 2. Execute manual ruin selling
goodyManager.sellRuins()

// 3. View performance summary
goodyManager.getTraceSummary()
// Output:
// 📊 Ruin Selling Trace Summary:
//    Total Calls: 5
//    Successful: 4
//    Failed: 1
//    Average Time: 23.45ms

// 4. View detailed trace log
goodyManager.showTraceLog()

// 5. Stop tracing
goodyManager.stopTracing()
```

## 📋 Structured Logging Output

### **Trace Log Format**
```
=== RUIN_SELLING_TRACE_REPORT ===
Timestamp: 2024-01-15T10:30:45.123Z
Total Calls: 5
Successful: 4
Failed: 1
Average Execution Time: 23.45ms

[2024-01-15T10:30:45.123Z] SellRuins
  TraceID: trace_1705315845123_abc123
  Session: ruin-sell-1705315845120
  Instance: 0x12345678 (EntityController)
  Status: COMPLETED
  Duration: 25ms
  Parameters: [{"type":"ValidatedInstance","name":"validation"},{"type":"RuinInfo","name":"ruinInfo"}]
  Return: {"type":"boolean","value":true,"name":"success"}

[2024-01-15T10:30:45.150Z] ClearDebris
  TraceID: trace_1705315845150_def456
  Session: ruin-sell-1705315845148
  Instance: 0x87654321 (EntityController)
  Status: ERROR
  Duration: 15ms
  Error: Method invocation failed: abort was called
```

### **Real-Time Tracing Output**
```
📞 [trace_1705315845123_abc123] SellRuins called on entity 45 (session: ruin-sell-1705315845120)
✅ [trace_1705315845123_abc123] Method completed with result: {"type":"boolean","value":true,"name":"success"}

📞 [trace_1705315845150_def456] ClearDebris called on entity 67 (session: ruin-sell-1705315845148)
❌ [trace_1705315845150_def456] Method failed: Method invocation failed: abort was called
```

## 🔍 Debugging Capabilities

### **Error Analysis**
- **Method Invocation Failures**: Tracks Il2Cpp method call failures
- **Parameter Issues**: Logs parameter types and values for debugging
- **State Inconsistencies**: Captures before/after state for comparison
- **Performance Bottlenecks**: Identifies slow method executions
- **Crash Prevention**: Safe error handling prevents tracer from crashing the game

### **Integration Safety**
- **Non-Intrusive**: Tracer doesn't interfere with normal ruin selling operation
- **Error Isolation**: Tracer failures don't affect ruin selling functionality
- **Performance Impact**: Minimal overhead when tracing is disabled
- **Memory Management**: Automatic trace history size limiting

## 🛡️ Error Handling and Safety

### **Robust Error Handling**
```typescript
// Safe method invocation with tracing
try {
    const sellResult = this.safeInvoke(validation.instance, ruinInfo.sellMethod);
    // Trace successful completion
    if (traceId && this.ruinSellingTracer) {
        this.ruinSellingTracer.traceMethodCompletion(traceId, result);
    }
} catch (error) {
    // Trace error without breaking functionality
    if (traceId && this.ruinSellingTracer) {
        this.ruinSellingTracer.traceMethodError(traceId, error as Error);
    }
    // Continue with normal error handling
    throw error;
}
```

### **Graceful Degradation**
- **Tracer Unavailable**: System works normally without tracer
- **Invalid Trace IDs**: Handles invalid trace IDs gracefully
- **Memory Limits**: Automatically manages trace history size
- **Configuration Errors**: Safe defaults for all configuration options

## 📁 Files Created

- **`src/ruin-selling-tracer.ts`** - Main tracer implementation
- **`tests/ruin-selling-tracer.test.ts`** - Comprehensive unit tests
- **`src/robust-instance-handler.ts`** - Updated with tracer integration
- **`RUIN_SELLING_TRACER_DOCUMENTATION.md`** - This documentation
- **`dist/robust-handler.js`** - Compiled with tracer functionality

## ✅ Implementation Complete

The Ruin Selling Tracer provides comprehensive execution tracing for the manual ruin selling functionality:

1. **Test-Driven Development**: 95+ unit tests covering all functionality
2. **Detailed Execution Tracing**: Complete method call lifecycle tracking
3. **Performance Metrics**: Timing analysis and performance summaries
4. **State Tracking**: Before/after instance state capture
5. **Error Analysis**: Comprehensive error tracking and reporting
6. **User-Friendly Interface**: Easy-to-use commands integrated into goodyManager
7. **Safety First**: Non-intrusive design that doesn't affect normal operation
8. **Structured Logging**: Professional trace reports for debugging

The tracer is now ready to help debug issues with the manual ruin selling functionality and provide insights into how the `SellRuins()` method behaves when called through the Il2Cpp interop layer! 🎉
